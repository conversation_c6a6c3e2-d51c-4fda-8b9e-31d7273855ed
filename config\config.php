<?php
// define('DB_HOST', 'localhost');
// define('DB_NAME', 'DB_data');
// define('DB_USER', 'root');
// define('DB_PASS', '');

define('DB_HOST', 'bl7tlhd6pqdrgnrw0tkj-mysql.services.clever-cloud.com');
define('DB_NAME', 'bl7tlhd6pqdrgnrw0tkj');
define('DB_USER', 'uci0vbkzf4zm3w6h');
define('DB_PASS', '3IujUlx309JLafcri5p2');

// Site configuration
define('SITE_NAME', 'سلسلة الدكتور');
define('SITE_URL', 'http://localhost/manash');
define('SITE_DESCRIPTION', 'منصة سلسلة الدكتور لتعليم اللغة العربية');

// URL Routing configuration
define('ENABLE_CLEAN_URLS', false);
define('ERROR_404_PAGE', 'error/404.php');

// Security settings
define('SESSION_TIMEOUT', 3600); // 1 hour
define('PASSWORD_MIN_LENGTH', 8);
define('MIN_AGE', 7); // Minimum age for registration

// Education levels and grades
define('EDUCATION_LEVELS', [
    'primary' => 'ابتدائي',
    'preparatory' => 'إعدادي',
    'secondary' => 'ثانوي'
]);

define('EDUCATION_TYPES', [
    'azhari' => 'أزهري',
    'general' => 'عام'
]);

define('GRADES', [
    'primary' => ['5' => 'الخامس الابتدائي', '6' => 'السادس الابتدائي'],
    'preparatory' => ['1' => 'الأول الإعدادي', '2' => 'الثاني الإعدادي', '3' => 'الثالث الإعدادي'],
    'secondary' => ['1' => 'الأول الثانوي', '2' => 'الثاني الثانوي', '3' => 'الثالث الثانوي']
]);

define('SECONDARY_SPECIALIZATIONS', [
    'scientific' => 'علمي',
    'literary' => 'أدبي'
]);

// Phone number prefixes
define('PHONE_PREFIXES', ['010', '011', '012', '015']);

// Error messages
define('ERROR_MESSAGES', [
    'username_exists' => 'اسم المستخدم موجود بالفعل',
    'email_exists' => 'البريد الإلكتروني موجود بالفعل',
    'invalid_email' => 'البريد الإلكتروني غير صحيح',
    'password_weak' => 'كلمة المرور ضعيفة',
    'passwords_not_match' => 'كلمات المرور غير متطابقة',
    'invalid_phone' => 'رقم الهاتف غير صحيح',
    'invalid_age' => 'العمر يجب أن يكون 7 سنوات على الأقل',
    'arabic_only' => 'يجب أن يحتوي على أحرف عربية فقط',
    'required_field' => 'هذا الحقل مطلوب'
]);

// Success messages
define('SUCCESS_MESSAGES', [
    'registration_success' => 'تم إنشاء الحساب بنجاح',
    'login_success' => 'تم تسجيل الدخول بنجاح'
]);

// Payment Gateway Settings
define('PAYMENT_GATEWAYS', [
    'fawry' => [
        'enabled' => true,
        'merchant_code' => 'YOUR_FAWRY_MERCHANT_CODE',
        'security_key' => 'YOUR_FAWRY_SECURITY_KEY',
        'base_url' => 'https://atfawry.fawrystaging.com', // Use https://www.atfawry.com for production
        'callback_url' => SITE_URL . '/api/fawry_callback.php',
        'return_url' => SITE_URL . '/page/payment_success.php'
    ],
    'paymob' => [
        'enabled' => true,
        'api_key' => 'ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TVRBMk1qSXdNeXdpYm1GdFpTSTZJakUzTlRNeE5Ua3pOall1TlRNeE5UZzVJbjAueHp5NlktZ0xZb0VHWncyeHY5czd2emlRLU1fMFE0dUNTQVVFeTFyY2UtSFpLeTBVQmNpaUpicUxmRHJtSVh6U29KZmNFSHQwTS1OMU1qOWxjRWR2NXc=',
        'integration_id' => '5207432', // Card payment integration (Visa, Mastercard, Meeza)
        'iframe_id' => '942105',
        'hmac_secret' => 'BB6DF653D8E6EBF3C6D3A6C750DBF3B3',
        'base_url' => 'https://accept.paymob.com/api',
        'callback_url' => SITE_URL . '/api/paymob_callback.php',
        'return_url' => SITE_URL . '/page/payment_success.php',
        // دعم أنواع البطاقات المختلفة
        'supported_cards' => [
            'visa' => true,
            'mastercard' => true,
            'meeza' => true,
            'debit' => true,
            'credit' => true
        ],
        'wallet_integrations' => [
            // استخدام نفس Integration ID للبطاقات مؤقتاً حتى يتم الحصول على IDs صحيحة للمحافظ
            'vodafone_cash' => '5207432',
            'etisalat_cash' => '5207432',
            'we_cash' => '5207432',
            'orange_cash' => '5207432'
        ]
    ]
]);

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

?>